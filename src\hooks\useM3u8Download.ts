import { useCallback } from 'react';
import type { DownloadItem, DownloadProgress, UseM3u8DownloadProps, LiveRecordingController } from '../types';
import { DownloadStatus } from '../types';
import {
  parseM3u8Content,
  parseM3u8ContentWithLiveDetection,
  downloadSegmentWithProgress,
  getSegmentsInfo,
  formatSize,
  formatSpeed,
  formatRemainingTime,
  type SegmentInfo
} from '../utils/m3u8Parser';
import { getVideoDurationFromBlob } from '../utils/videoUtils';
import { downloadFileWithHeaders } from '../utils/downloadUtils';

/**
 * M3U8下载处理Hook
 * 负责M3U8流媒体文件的下载逻辑
 */
export function useM3u8Download({
  state,
  updateProgress,
  setDownloadBlobUrl,
  updateDownloadDataDuration,
  saveToLocal,
  setLiveRecordingController
}: UseM3u8DownloadProps) {

  // M3U8文件下载处理
  const downloadM3u8File = useCallback(async (item: DownloadItem) => {
    try {
      // 检查页面任务ID
      if (!state.pageTaskId) {
        throw new Error('页面任务ID不存在，无法开始下载');
      }

      // 检查是否已保存，避免重复保存
      if (state.savedFiles.has(item.requestId)) {
        console.log(`M3U8文件已保存，跳过重复保存: ${item.filename}`);
        updateProgress(item.requestId, {
          status: DownloadStatus.COMPLETED,
          statusText: '已保存',
          percentage: 100
        });
        return;
      }

      // 第一步：设置请求头规则（确保M3U8和TS分片请求使用正确的Origin和Referer）
      updateProgress(item.requestId, {
        status: DownloadStatus.DOWNLOADING,
        statusText: '设置请求头...',
        percentage: 0
      });

      const result = await downloadFileWithHeaders(item, state.pageTaskId);
      console.log(`M3U8请求头设置完成: ${result.data?.filename || item.filename}`);

      // 第二步：获取M3U8播放列表
      updateProgress(item.requestId, {
        status: DownloadStatus.DOWNLOADING,
        statusText: '获取M3U8播放列表...',
        percentage: 0
      });

      const m3u8Response = await fetch(item.url, {
        headers: item.requestHeaders
          ? Object.fromEntries(
            item.requestHeaders.map((h) => [h.name, h.value])
          )
          : {}
      });

      if (!m3u8Response.ok) {
        throw new Error(`获取M3U8失败: ${m3u8Response.status}`);
      }

      const m3u8Content = await m3u8Response.text();
      console.log('M3U8内容:', m3u8Content);

      // 第三步：解析M3U8内容并检测直播流
      updateProgress(item.requestId, {
        status: DownloadStatus.DOWNLOADING,
        statusText: '解析视频分片...',
        percentage: 0
      });

      const parseResult = parseM3u8ContentWithLiveDetection(m3u8Content, item.url);
      let segments = parseResult.segments;
      const liveStreamInfo = parseResult.liveStreamInfo;

      console.log('直播流检测结果:', liveStreamInfo);

      // 如果检测到直播流，更新下载项标识
      if (liveStreamInfo.isLiveStream) {
        item.isLiveStream = true;
        console.log(`检测到M3U8直播流: ${liveStreamInfo.reason}`);

        updateProgress(item.requestId, {
          status: DownloadStatus.LIVE_RECORDING,
          statusText: '检测到直播流，开始录制...',
          percentage: 10
        });
      }

      // 检查是否需要二次解析（主播放列表返回了子播放列表URL）
      if (
        segments.length === 1 &&
        (segments[0].includes('.m3u8') || segments[0].includes('m3u8'))
      ) {
        updateProgress(item.requestId, {
          status: DownloadStatus.DOWNLOADING,
          statusText: '获取子播放列表...',
          percentage: 0
        });

        const subResponse = await fetch(segments[0], {
          headers: item.requestHeaders
            ? Object.fromEntries(
              item.requestHeaders.map((h) => [h.name, h.value])
            )
            : {}
        });

        if (!subResponse.ok) {
          throw new Error(`获取子播放列表失败: ${subResponse.status}`);
        }

        const subContent = await subResponse.text();
        console.log('子播放列表内容:', subContent);
        segments = parseM3u8Content(subContent, segments[0]);
      }

      console.log(`解析到 ${segments.length} 个视频分片`);

      if (segments.length === 0) {
        throw new Error('没有找到视频分片');
      }

      // 第三步：根据是否为直播流选择不同的下载策略
      let downloadedSegments: ArrayBuffer[];

      if (item.isLiveStream) {
        // 直播流：持续录制模式
        downloadedSegments = await downloadLiveStream(
          segments,
          item,
          updateProgress,
          setLiveRecordingController
        );
      } else {
        // 点播文件：一次性下载所有分片
        downloadedSegments = await downloadAllSegments(
          segments,
          item,
          updateProgress
        );
      }

      // 第四步：合并分片并创建Blob URL
      await mergeSegments(
        downloadedSegments,
        item,
        updateProgress,
        setDownloadBlobUrl,
        updateDownloadDataDuration,
        saveToLocal
      );

      console.log(`M3U8视频下载完成: ${item.filename}`);
    } catch (error) {
      console.error('M3U8下载失败:', error);
      updateProgress(item.requestId, {
        status: DownloadStatus.ERROR,
        statusText: 'M3U8下载失败: ' + (error as Error).message,
        percentage: 0
      });
      throw error;
    }
  }, [state.savedFiles, updateProgress, setDownloadBlobUrl, updateDownloadDataDuration, saveToLocal, setLiveRecordingController]);

  return {
    downloadM3u8File
  };
}

/**
 * 下载所有分片（优化版本，支持真实进度和大小）
 */
async function downloadAllSegments(
  segments: string[],
  item: DownloadItem,
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void
): Promise<ArrayBuffer[]> {
  // 第一步：获取所有分片的大小信息
  updateProgress(item.requestId, {
    status: DownloadStatus.DOWNLOADING,
    statusText: '分析分片信息...',
    percentage: 0
  });

  let segmentsInfo: SegmentInfo[];
  let totalSize = 0;

  try {
    segmentsInfo = await getSegmentsInfo(
      segments,
      item.requestHeaders,
      (current, total) => {
        updateProgress(item.requestId, {
          status: DownloadStatus.DOWNLOADING,
          statusText: `分析分片信息 (${current}/${total})`,
          percentage: (current / total) * 10 // 分析阶段占10%进度
        });
      }
    );

    totalSize = segmentsInfo.reduce((sum, segment) => sum + segment.size, 0);

    console.log(`M3U8分片分析完成: ${segments.length}个分片, 总大小: ${formatSize(totalSize)}`);
  } catch (error) {
    console.warn('获取分片大小失败，使用传统下载方式:', error);
    // 降级到传统方式
    return downloadAllSegmentsLegacy(segments, item, updateProgress);
  }

  // 第二步：下载所有分片
  const downloadedSegments: ArrayBuffer[] = [];
  let downloadedSize = 0;
  const startTime = Date.now();
  let lastUpdateTime = startTime;

  for (let i = 0; i < segmentsInfo.length; i++) {
    const segmentInfo = segmentsInfo[i];

    try {
      const segmentData = await downloadSegmentWithProgress(
        segmentInfo.url,
        item.requestHeaders,
        (segmentDownloaded, segmentTotal) => {
          const currentTime = Date.now();

          // 限制更新频率，避免过于频繁的UI更新
          if (currentTime - lastUpdateTime < 100) return;
          lastUpdateTime = currentTime;

          const currentSegmentProgress = segmentDownloaded;
          const totalDownloaded = downloadedSize + currentSegmentProgress;

          // 计算下载速度
          const elapsedSeconds = (currentTime - startTime) / 1000;
          const speed = elapsedSeconds > 0 ? totalDownloaded / elapsedSeconds : 0;

          // 计算进度百分比（下载阶段占80%，10%-90%）
          const percentage = totalSize > 0
            ? 10 + (totalDownloaded / totalSize) * 80
            : 10 + ((i + segmentDownloaded / segmentTotal) / segmentsInfo.length) * 80;

          // 计算剩余时间
          const remainingBytes = totalSize - totalDownloaded;
          const remainingTime = speed > 0 ? formatRemainingTime(remainingBytes, speed) : '计算中...';

          updateProgress(item.requestId, {
            status: DownloadStatus.DOWNLOADING,
            statusText: `下载中 (${i + 1}/${segmentsInfo.length}) - ${formatSize(totalDownloaded)}/${formatSize(totalSize)} - ${formatSpeed(speed)} - 剩余${remainingTime}`,
            percentage: Math.min(90, percentage),
            downloadedSize: totalDownloaded,
            totalSize: totalSize,
            speed: speed
          });
        }
      );

      downloadedSegments.push(segmentData);
      downloadedSize += segmentData.byteLength;

    } catch (error) {
      console.error(`下载分片失败 (${i + 1}/${segmentsInfo.length}):`, error);
      throw error;
    }
  }

  // 最终更新进度
  const finalTime = Date.now();
  const totalElapsedSeconds = (finalTime - startTime) / 1000;
  const averageSpeed = totalElapsedSeconds > 0 ? downloadedSize / totalElapsedSeconds : 0;

  updateProgress(item.requestId, {
    status: DownloadStatus.DOWNLOADING,
    statusText: `下载完成 - ${formatSize(downloadedSize)} - 平均速度 ${formatSpeed(averageSpeed)}`,
    percentage: 90,
    downloadedSize: downloadedSize,
    totalSize: totalSize,
    speed: averageSpeed
  });

  return downloadedSegments;
}

/**
 * 传统下载方式（降级方案）
 */
async function downloadAllSegmentsLegacy(
  segments: string[],
  item: DownloadItem,
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void
): Promise<ArrayBuffer[]> {
  const downloadedSegments: ArrayBuffer[] = [];

  for (let i = 0; i < segments.length; i++) {
    updateProgress(item.requestId, {
      status: DownloadStatus.DOWNLOADING,
      statusText: `下载分片 (${i + 1}/${segments.length})`,
      percentage: 10 + (i / segments.length) * 80 // 10% - 90%
    });

    const segmentData = await downloadSegmentWithProgress(
      segments[i],
      item.requestHeaders
    );
    downloadedSegments.push(segmentData);
  }

  return downloadedSegments;
}

/**
 * 合并分片并创建Blob URL
 */
async function mergeSegments(
  downloadedSegments: ArrayBuffer[],
  item: DownloadItem,
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void,
  setDownloadBlobUrl: (blobUrl: string | null) => void,
  updateDownloadDataDuration: (duration: string | null) => void,
  saveToLocal: (blobUrl?: string) => Promise<void>
): Promise<void> {
  // 合并阶段开始
  updateProgress(item.requestId, {
    status: DownloadStatus.DOWNLOADING,
    statusText: '合并分片中...',
    percentage: 95
  });

  // 合并分片
  const mergedBlob = new Blob(downloadedSegments, { type: 'video/mp4' });
  const finalSize = mergedBlob.size;

  console.log(`M3U8分片合并完成: ${downloadedSegments.length}个分片, 最终大小: ${formatSize(finalSize)}`);

  // 创建 Blob URL，等待用户手动保存
  const blobUrl = URL.createObjectURL(mergedBlob);

  // 保存 Blob URL 到状态
  setDownloadBlobUrl(blobUrl);

  // 尝试获取文件时长
  updateProgress(item.requestId, {
    status: DownloadStatus.DOWNLOADING,
    statusText: '获取视频信息中...',
    percentage: 98
  });

  const duration = await getVideoDurationFromBlob(blobUrl);
  if (duration) {
    updateDownloadDataDuration(duration);
    console.log('M3U8文件时长获取成功:', duration);
  }

  // 下载完成，自动触发保存
  updateProgress(item.requestId, {
    status: DownloadStatus.COMPLETED,
    statusText: `下载完成，正在保存到本地 (${formatSize(finalSize)})`,
    percentage: 100,
    downloadedSize: finalSize,
    totalSize: finalSize
  });

  // 自动触发保存
  try {
    await saveToLocal(blobUrl);
    console.log('M3U8文件已自动保存到本地:', item.filename);
  } catch (error) {
    console.error('自动保存失败:', error);
    updateProgress(item.requestId, {
      status: DownloadStatus.ERROR,
      statusText: '自动保存失败: ' + (error as Error).message,
      percentage: 100
    });
  }
}

/**
 * 直播流下载函数
 * 支持持续录制和AbortController中断
 */
async function downloadLiveStream(
  initialSegments: string[],
  item: DownloadItem,
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void,
  setLiveRecordingController: (controller: LiveRecordingController | null) => void
): Promise<ArrayBuffer[]> {
  const downloadedSegments: ArrayBuffer[] = [];
  let downloadedSize = 0;
  const startTime = Date.now();
  let lastUpdateTime = startTime;
  let segmentCount = 0;

  // 创建AbortController用于停止录制
  const abortController = new AbortController();

  // 创建直播录制控制器
  const liveController: LiveRecordingController = {
    requestId: item.requestId,
    abortController: abortController,
    isRecording: true,
    recordedSegments: downloadedSegments,
    startTime: startTime,
    stop: async () => {
      console.log('停止直播录制:', item.requestId);
      abortController.abort();
      liveController.isRecording = false;
    }
  };

  // 将直播录制控制器保存到状态中
  setLiveRecordingController(liveController);
  console.log('直播录制控制器已保存到状态:', liveController);

  try {
    // 下载初始分片
    for (let i = 0; i < initialSegments.length && !abortController.signal.aborted; i++) {
      const segmentUrl = initialSegments[i];
      segmentCount++;

      updateProgress(item.requestId, {
        status: DownloadStatus.LIVE_RECORDING,
        statusText: `录制中 (分片 ${segmentCount})`,
        percentage: Math.min(90, (segmentCount / Math.max(initialSegments.length, 10)) * 90)
      });

      try {
        const segmentData = await downloadSegmentWithProgress(
          segmentUrl,
          item.requestHeaders,
          (segmentDownloaded) => {
            const currentTime = Date.now();

            // 限制更新频率
            if (currentTime - lastUpdateTime < 200) return;
            lastUpdateTime = currentTime;

            const totalDownloaded = downloadedSize + segmentDownloaded;
            const elapsedSeconds = (currentTime - startTime) / 1000;
            const speed = elapsedSeconds > 0 ? totalDownloaded / elapsedSeconds : 0;

            updateProgress(item.requestId, {
              status: DownloadStatus.LIVE_RECORDING,
              statusText: `录制中 (分片 ${segmentCount}) - ${formatSize(totalDownloaded)} - ${formatSpeed(speed)}`,
              percentage: Math.min(90, (segmentCount / Math.max(initialSegments.length, 10)) * 90),
              downloadedSize: totalDownloaded,
              speed: speed
            });
          }
        );

        downloadedSegments.push(segmentData);
        downloadedSize += segmentData.byteLength;

      } catch (error) {
        if (abortController.signal.aborted) {
          console.log('直播录制被用户停止');
          break;
        }
        console.error(`下载直播分片失败 (${segmentCount}):`, error);
        // 对于直播流，单个分片失败不应该中断整个录制
        continue;
      }
    }

    // 检查是否被中断
    if (abortController.signal.aborted) {
      updateProgress(item.requestId, {
        status: DownloadStatus.COMPLETED,
        statusText: `录制已停止 - 共录制 ${segmentCount} 个分片 (${formatSize(downloadedSize)})`,
        percentage: 100,
        downloadedSize: downloadedSize
      });
    } else {
      // 正常完成（所有已知分片下载完毕）
      updateProgress(item.requestId, {
        status: DownloadStatus.COMPLETED,
        statusText: `录制完成 - 共录制 ${segmentCount} 个分片 (${formatSize(downloadedSize)})`,
        percentage: 100,
        downloadedSize: downloadedSize
      });
    }

    // 清理直播录制控制器状态
    setLiveRecordingController(null);
    console.log('直播录制控制器已清理');

  } catch (error) {
    if (!abortController.signal.aborted) {
      console.error('直播录制失败:', error);
      // 清理直播录制控制器状态
      setLiveRecordingController(null);
      throw error;
    }
  }

  return downloadedSegments;
}
