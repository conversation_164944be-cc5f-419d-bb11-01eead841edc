<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M3U8 视频播放器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            margin-bottom: 20px;
        }
        
        video {
            width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .success {
            color: #155724;
            background-color: #d4edda;
            border-left-color: #28a745;
        }
    </style>
    <!-- 引入 hls.js 库来支持 m3u8 播放 -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
</head>
<body>
    <div class="container">
        <h1>M3U8 视频播放器</h1>
        
        <div class="video-container">
            <video id="video" controls autoplay muted>
                您的浏览器不支持视频播放。
            </video>
        </div>
        
        <div class="controls">
            <button onclick="playVideo()">播放</button>
            <button onclick="pauseVideo()">暂停</button>
            <button onclick="reloadVideo()">重新加载</button>
        </div>
        
        <div id="info" class="info">
            正在加载视频流...
        </div>
    </div>

    <script>
        const video = document.getElementById('video');
        const infoDiv = document.getElementById('info');
        const m3u8Url = 'http://devimages.apple.com/iphone/samples/bipbop/gear1/prog_index.m3u8';
        
        let hls;
        
        function showInfo(message, type = 'info') {
            infoDiv.textContent = message;
            infoDiv.className = `info ${type}`;
        }
        
        function initPlayer() {
            if (Hls.isSupported()) {
                hls = new Hls({
                    debug: false,
                    enableWorker: true,
                    lowLatencyMode: true,
                });
                
                hls.loadSource(m3u8Url);
                hls.attachMedia(video);
                
                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    showInfo('视频流加载成功，准备播放', 'success');
                    video.play().catch(e => {
                        showInfo('自动播放失败，请手动点击播放按钮', 'error');
                        console.error('播放失败:', e);
                    });
                });
                
                hls.on(Hls.Events.ERROR, function(event, data) {
                    console.error('HLS错误:', data);
                    if (data.fatal) {
                        switch(data.type) {
                            case Hls.ErrorTypes.NETWORK_ERROR:
                                showInfo('网络错误，正在尝试恢复...', 'error');
                                hls.startLoad();
                                break;
                            case Hls.ErrorTypes.MEDIA_ERROR:
                                showInfo('媒体错误，正在尝试恢复...', 'error');
                                hls.recoverMediaError();
                                break;
                            default:
                                showInfo('播放器发生致命错误', 'error');
                                hls.destroy();
                                break;
                        }
                    }
                });
                
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // Safari 原生支持 HLS
                video.src = m3u8Url;
                showInfo('使用原生HLS支持播放', 'success');
                video.addEventListener('loadedmetadata', function() {
                    video.play().catch(e => {
                        showInfo('自动播放失败，请手动点击播放按钮', 'error');
                        console.error('播放失败:', e);
                    });
                });
            } else {
                showInfo('您的浏览器不支持HLS播放', 'error');
            }
        }
        
        function playVideo() {
            video.play().then(() => {
                showInfo('视频正在播放', 'success');
            }).catch(e => {
                showInfo('播放失败: ' + e.message, 'error');
            });
        }
        
        function pauseVideo() {
            video.pause();
            showInfo('视频已暂停', 'info');
        }
        
        function reloadVideo() {
            if (hls) {
                hls.destroy();
            }
            showInfo('正在重新加载视频流...', 'info');
            setTimeout(initPlayer, 500);
        }
        
        // 页面加载完成后初始化播放器
        document.addEventListener('DOMContentLoaded', function() {
            initPlayer();
        });
        
        // 监听视频事件
        video.addEventListener('play', () => showInfo('视频开始播放', 'success'));
        video.addEventListener('pause', () => showInfo('视频已暂停', 'info'));
        video.addEventListener('ended', () => showInfo('视频播放完成', 'info'));
        video.addEventListener('error', (e) => showInfo('视频播放出错', 'error'));
    </script>
</body>
</html>
