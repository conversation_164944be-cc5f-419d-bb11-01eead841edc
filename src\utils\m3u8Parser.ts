import type { M3u8Variant, M3u8QualityOption, LiveStreamDetectionResult } from '../types';

// ==================== 常量定义 ====================
const M3U8_TAGS = {
  EXTM3U: '#EXTM3U',
  STREAM_INF: '#EXT-X-STREAM-INF',
  I_FRAME_STREAM_INF: '#EXT-X-I-FRAME-STREAM-INF',
  ENDLIST: '#EXT-X-ENDLIST', // 播放列表结束标识
} as const;

const SEGMENT_EXTENSIONS = ['.ts', '.m4s'] as const;

const QUALITY_LEVELS = {
  1080: '1080p',
  720: '720p',
  480: '480p',
  360: '360p',
  240: '240p',
} as const;

const REGEX_PATTERNS = {
  BANDWIDTH: /BANDWIDTH=(\d+)/,
  RESOLUTION: /RESOLUTION=(\d+x\d+)/,
} as const;

// ==================== 主要解析函数 ====================

/**
 * 解析M3U8内容获取分片URL
 * @param content M3U8文件内容
 * @param baseUrl 基础URL
 * @returns 分片URL数组
 */
export function parseM3u8Content(content: string, baseUrl: string): string[] {
  if (!content?.trim() || !baseUrl?.trim()) {
    throw new Error('M3U8内容和基础URL不能为空');
  }

  const isMasterPlaylist = checkIsMasterPlaylist(content);

  if (isMasterPlaylist) {
    // 主播放列表：选择最高质量的子播放列表
    return parseMasterPlaylist(content, baseUrl);
  } else {
    // 媒体播放列表：直接解析分片
    return parseMediaPlaylist(content, baseUrl);
  }
}

/**
 * 解析M3U8内容并检测直播流
 * @param content M3U8文件内容
 * @param baseUrl 基础URL
 * @param totalSize 总大小（可选）
 * @returns 包含分片URL和直播流检测结果的对象
 */
export function parseM3u8ContentWithLiveDetection(
  content: string,
  baseUrl: string,
  totalSize?: number
): { segments: string[]; liveStreamInfo: LiveStreamDetectionResult } {
  if (!content?.trim() || !baseUrl?.trim()) {
    throw new Error('M3U8内容和基础URL不能为空');
  }

  // 解析分片URL
  const segments = parseM3u8Content(content, baseUrl);

  // 检测直播流
  const liveStreamInfo = checkIsLiveStream(content, totalSize);

  return {
    segments,
    liveStreamInfo
  };
}

// ==================== 播放列表类型检测 ====================

/**
 * 检查是否为主播放列表
 */
function checkIsMasterPlaylist(content: string): boolean {
  return (
    content.includes(M3U8_TAGS.STREAM_INF) ||
    content.includes(M3U8_TAGS.I_FRAME_STREAM_INF) ||
    hasMultiplePlaylistsInContent(content)
  );
}

/**
 * 检查内容是否包含多个播放列表
 */
function hasMultiplePlaylistsInContent(content: string): boolean {
  const extm3uMatches = content.match(new RegExp(M3U8_TAGS.EXTM3U, 'g'));
  return (extm3uMatches?.length ?? 0) > 1;
}

/**
 * 检测是否为直播流
 * @param content M3U8文件内容
 * @param totalSize 总大小（可选，仅作为辅助判断）
 * @returns 直播流检测结果
 */
export function checkIsLiveStream(content: string, totalSize?: number): LiveStreamDetectionResult {
  if (!content?.trim()) {
    return {
      isLiveStream: false,
      hasEndList: false,
      reason: 'M3U8内容为空'
    };
  }

  const hasEndList = content.includes(M3U8_TAGS.ENDLIST);

  // 主要判断依据：EXT-X-ENDLIST标识
  // 有EXT-X-ENDLIST标识表示这是一个完整的点播文件
  if (hasEndList) {
    return {
      isLiveStream: false,
      hasEndList: true,
      totalSize,
      reason: '有EXT-X-ENDLIST标识，判定为点播文件'
    };
  }

  // 没有EXT-X-ENDLIST标识，判定为直播流
  return {
    isLiveStream: true,
    hasEndList: false,
    totalSize,
    reason: '缺少EXT-X-ENDLIST标识，判定为直播流'
  };
}

// ==================== 主播放列表解析 ====================

/**
 * 解析主播放列表，选择最佳质量
 */
function parseMasterPlaylist(content: string, baseUrl: string): string[] {
  const lines = splitAndCleanLines(content);
  const variants = extractVariants(lines);

  if (variants.length === 0) {
    // 如果没有找到标准的变体流，尝试解析多个独立的播放列表
    return parseMultipleIndependentPlaylists(content, baseUrl);
  }

  const bestVariant = selectBestVariant(variants);
  logSelectedVariant(bestVariant);

  const variantUrl = resolveUrl(bestVariant.url, baseUrl);
  return [variantUrl]; // 返回子播放列表URL，需要进一步解析
}

/**
 * 从行数组中提取变体流信息
 */
function extractVariants(lines: string[]): M3u8Variant[] {
  const variants: M3u8Variant[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    if (line.startsWith(M3U8_TAGS.STREAM_INF)) {
      const bandwidth = extractBandwidth(line);
      const resolution = extractResolution(line);
      const nextLine = lines[i + 1];

      if (nextLine && !nextLine.startsWith('#')) {
        variants.push({
          bandwidth: bandwidth ?? 0,
          resolution: resolution ?? '',
          url: nextLine
        });
      }
    }
  }

  return variants;
}

/**
 * 选择最高码率的变体
 */
function selectBestVariant(variants: M3u8Variant[]): M3u8Variant {
  return variants.reduce((best, current) =>
    current.bandwidth > best.bandwidth ? current : best
  );
}

/**
 * 记录选择的变体信息
 */
function logSelectedVariant(variant: M3u8Variant): void {
  console.log(
    `选择最高质量流: ${variant.resolution || '未知分辨率'}, 码率: ${variant.bandwidth}`
  );
}

// ==================== 工具函数 ====================

/**
 * 分割并清理行内容
 */
function splitAndCleanLines(content: string): string[] {
  return content
    .split('\n')
    .map((line) => line.trim())
    .filter((line) => line);
}

/**
 * 解析相对URL为绝对URL
 */
function resolveUrl(url: string, baseUrl: string): string {
  if (url.startsWith('http')) {
    return url;
  }

  const baseUrlObj = new URL(baseUrl);
  const basePath = baseUrlObj.pathname.substring(
    0,
    baseUrlObj.pathname.lastIndexOf('/') + 1
  );

  return new URL(url, baseUrlObj.origin + basePath).href;
}

// ==================== 多播放列表解析 ====================

/**
 * 解析多个独立播放列表
 */
function parseMultipleIndependentPlaylists(content: string, baseUrl: string): string[] {
  const playlists = splitPlaylists(content);

  if (playlists.length <= 1) {
    return parseMediaPlaylist(content, baseUrl);
  }

  const qualityOptions = analyzePlaylistQualities(playlists);

  if (qualityOptions.length === 0) {
    return handleNoQualityOptions(playlists, baseUrl);
  }

  const bestOption = selectBestQualityOption(qualityOptions, playlists.length);
  return parseMediaPlaylist(bestOption.content, baseUrl);
}

/**
 * 分割多个播放列表
 */
function splitPlaylists(content: string): string[] {
  return content.split(M3U8_TAGS.EXTM3U).filter((part) => part.trim());
}

/**
 * 分析播放列表质量
 */
function analyzePlaylistQualities(playlists: string[]): M3u8QualityOption[] {
  const qualityOptions: M3u8QualityOption[] = [];

  for (const playlist of playlists) {
    const playlistContent = M3U8_TAGS.EXTM3U + '\n' + playlist.trim();
    const quality = estimatePlaylistQuality(playlistContent);

    if (quality > 0) {
      qualityOptions.push({
        quality,
        qualityLabel: getQualityLabel(quality),
        content: playlistContent
      });
    }
  }

  // 按质量排序（高到低）
  return qualityOptions.sort((a, b) => b.quality - a.quality);
}

/**
 * 处理无法识别质量的情况
 */
function handleNoQualityOptions(playlists: string[], baseUrl: string): string[] {
  const firstPlaylist = M3U8_TAGS.EXTM3U + '\n' + playlists[0].trim();
  console.log('无法识别质量，使用第一个播放列表');
  return parseMediaPlaylist(firstPlaylist, baseUrl);
}

/**
 * 选择最佳质量选项并记录日志
 */
function selectBestQualityOption(qualityOptions: M3u8QualityOption[], totalCount: number): M3u8QualityOption {
  const bestOption = qualityOptions[0];

  console.log(
    `从${totalCount}个播放列表中选择最高质量版本: ${bestOption.qualityLabel}`
  );

  const availableQualities = qualityOptions
    .map((opt) => opt.qualityLabel)
    .join(', ');
  console.log(`可用质量选项: ${availableQualities}`);

  return bestOption;
}

// ==================== 质量分析 ====================

/**
 * 获取质量标签
 */
function getQualityLabel(quality: number): string {
  return QUALITY_LEVELS[quality as keyof typeof QUALITY_LEVELS] ?? `${quality}p`;
}

/**
 * 估算播放列表质量（基于文件名中的分辨率标识）
 */
function estimatePlaylistQuality(content: string): number {
  const resolutionMap = {
    '1080': 1080,
    '720': 720,
    '480': 480,
    '360': 360,
    '240': 240
  } as const;

  for (const [resolutionStr, quality] of Object.entries(resolutionMap)) {
    if (content.includes(resolutionStr)) {
      return quality;
    }
  }

  return 0; // 未知质量
}

// ==================== 媒体播放列表解析 ====================

/**
 * 解析媒体播放列表获取TS分片
 */
export function parseMediaPlaylist(content: string, baseUrl: string): string[] {
  const lines = splitAndCleanLines(content);
  const segments: string[] = [];

  for (const line of lines) {
    if (isSegmentLine(line)) {
      const segmentUrl = resolveUrl(line, baseUrl);
      segments.push(segmentUrl);
    }
  }

  return segments;
}

/**
 * 判断是否为分片行
 */
function isSegmentLine(line: string): boolean {
  return (
    Boolean(line) &&
    !line.startsWith('#') &&
    SEGMENT_EXTENSIONS.some(ext => line.endsWith(ext))
  );
}

// ==================== 信息提取 ====================

/**
 * 提取码率信息
 */
function extractBandwidth(line: string): number | null {
  const match = line.match(REGEX_PATTERNS.BANDWIDTH);
  return match ? parseInt(match[1], 10) : null;
}

/**
 * 提取分辨率信息
 */
function extractResolution(line: string): string | null {
  const match = line.match(REGEX_PATTERNS.RESOLUTION);
  return match ? match[1] : null;
}

// ==================== 分片下载 ====================

/**
 * 分片信息接口
 */
export interface SegmentInfo {
  url: string;
  size: number;
  downloaded: number;
}

/**
 * 下载进度回调接口
 */
export interface DownloadProgressCallback {
  (downloaded: number, total: number): void;
}

/**
 * 获取分片大小
 * @param url 分片URL
 * @param requestHeaders 请求头
 * @returns 分片大小（字节）
 */
export async function getSegmentSize(
  url: string,
  requestHeaders?: Array<{ name: string; value: string }>
): Promise<number> {
  try {
    const headers = requestHeaders
      ? Object.fromEntries(requestHeaders.map((h) => [h.name, h.value]))
      : {};

    // 先尝试HEAD请求获取Content-Length
    try {
      const headResponse = await fetch(url, {
        method: 'HEAD',
        headers
      });

      if (headResponse.ok) {
        const contentLength = headResponse.headers.get('content-length');
        if (contentLength) {
          return parseInt(contentLength, 10);
        }
      }
    } catch (error) {
      console.warn('HEAD请求失败，尝试Range请求:', error);
    }

    // 如果HEAD请求失败，使用Range请求获取大小
    const rangeResponse = await fetch(url, {
      headers: {
        ...headers,
        'Range': 'bytes=0-0'
      }
    });

    if (rangeResponse.ok) {
      const contentRange = rangeResponse.headers.get('content-range');
      if (contentRange) {
        // Content-Range: bytes 0-0/1234567
        const match = contentRange.match(/bytes \d+-\d+\/(\d+)/);
        if (match) {
          return parseInt(match[1], 10);
        }
      }
    }

    // 如果都失败了，返回0表示未知大小
    console.warn('无法获取分片大小:', url);
    return 0;
  } catch (error) {
    console.warn('获取分片大小失败:', error);
    return 0;
  }
}

/**
 * 获取所有分片的大小信息
 * @param urls 分片URL数组
 * @param requestHeaders 请求头
 * @param onProgress 进度回调
 * @returns 分片信息数组
 */
export async function getSegmentsInfo(
  urls: string[],
  requestHeaders?: Array<{ name: string; value: string }>,
  onProgress?: (current: number, total: number) => void
): Promise<SegmentInfo[]> {
  const segments: SegmentInfo[] = [];

  // 限制并发数量，避免过多请求
  const concurrency = 5;
  const chunks = [];

  for (let i = 0; i < urls.length; i += concurrency) {
    chunks.push(urls.slice(i, i + concurrency));
  }

  let completed = 0;

  for (const chunk of chunks) {
    const promises = chunk.map(async (url) => {
      const size = await getSegmentSize(url, requestHeaders);
      return {
        url,
        size,
        downloaded: 0
      };
    });

    const chunkResults = await Promise.all(promises);
    segments.push(...chunkResults);

    completed += chunk.length;
    onProgress?.(completed, urls.length);
  }

  return segments;
}

/**
 * 带进度监控的分片下载
 * @param url 分片URL
 * @param requestHeaders 请求头
 * @param onProgress 进度回调
 * @returns 分片数据
 */
export async function downloadSegmentWithProgress(
  url: string,
  requestHeaders?: Array<{ name: string; value: string }>,
  onProgress?: DownloadProgressCallback
): Promise<ArrayBuffer> {
  try {
    const headers = requestHeaders
      ? Object.fromEntries(requestHeaders.map((h) => [h.name, h.value]))
      : {};

    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentLength = response.headers.get('content-length');
    const total = contentLength ? parseInt(contentLength, 10) : 0;

    if (!response.body) {
      throw new Error('响应体为空');
    }

    const reader = response.body.getReader();
    const chunks: Uint8Array[] = [];
    let downloaded = 0;

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        chunks.push(value);
        downloaded += value.length;

        // 调用进度回调
        if (onProgress && total > 0) {
          onProgress(downloaded, total);
        }
      }
    } finally {
      reader.releaseLock();
    }

    // 合并所有chunks
    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const result = new Uint8Array(totalLength);
    let offset = 0;

    for (const chunk of chunks) {
      result.set(chunk, offset);
      offset += chunk.length;
    }

    return result.buffer;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    throw new Error(`下载分片失败: ${errorMessage} - ${url}`);
  }
}

/**
 * 下载单个分片（保持向后兼容）
 * @param url 分片URL
 * @param requestHeaders 请求头
 * @returns 分片数据
 */
export async function downloadSegment(
  url: string,
  requestHeaders?: Array<{ name: string; value: string }>
): Promise<ArrayBuffer> {
  return downloadSegmentWithProgress(url, requestHeaders);
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化的大小字符串
 */
export function formatSize(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * 格式化下载速度
 * @param bytesPerSecond 每秒字节数
 * @returns 格式化的速度字符串
 */
export function formatSpeed(bytesPerSecond: number): string {
  return formatSize(bytesPerSecond) + '/s';
}

/**
 * 计算剩余时间
 * @param remainingBytes 剩余字节数
 * @param bytesPerSecond 每秒字节数
 * @returns 格式化的剩余时间字符串
 */
export function formatRemainingTime(remainingBytes: number, bytesPerSecond: number): string {
  if (bytesPerSecond <= 0) return '未知';

  const seconds = Math.ceil(remainingBytes / bytesPerSecond);

  if (seconds < 60) {
    return `${seconds}秒`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}分钟`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}小时${minutes}分钟`;
  }
}
